import numpy as np
import pandas as pd
from cegalprizm.pythontool import PetrelConnection, Well, WellLog, GlobalWellLog, DiscreteGlobalWellLog

# Start: PWR Description

from cegalprizm.pycoderunner import WorkflowDescription, DomainObjectsEnum

# The class WorkflowDescription is used to define the Cegal Prizm workflow. It is assigned to a Python variable called 'pwr_description' 
pwr_description = WorkflowDescription(name="Vsh Calculator",
                                      category="Bundled workflows",
                                      description="Calculate Vsh using different algorithms",
                                      authors="author@company",
                                      version="1.0")

# Use the variable pwr_description to define the UI in the Prizm Workflow Runner and let the Petrel user select the input data.
# This creates a Python dictionary 'parameters' with the GUID and/or values of the user's input data.

pwr_description.add_object_ref_parameter(name = "well_id", label = "Select well", description = "Select the well", object_type = DomainObjectsEnum.Well)
pwr_description.add_object_ref_parameter(name = "log_id", label = "Gamma ray input log", description = "Gamma ray log used to calculate Vshale", object_type = DomainObjectsEnum.WellContinuousLog, template_type = 'GammaRay', linked_input_name = "well_id")
pwr_description.add_integer_parameter(name = 'grs_input', label = 'Clean rock GR value:', description = 'The GammaRay value associated with a clean reservoir having no shale ', default_value = 15, minimum_value = 1, maximum_value = 1000)
pwr_description.add_integer_parameter(name = 'grsh_input', label = 'Shale GammaRay value', description = 'The GammaRay value associated with a zone of 100% shale', default_value = 120, minimum_value = 1, maximum_value = 1000)
pwr_description.add_enum_parameter(name = 'methods', label = 'Select method:', description = 'Choose which method you want to use for calculation Vsh', options = {0: 'Linear', 1: 'Larionov - tertiary rocks', 2: 'Larionov - older rocks', 3: 'Clavier', 4: 'Stieber'})
pwr_description.add_string_parameter(name = 'suffix', label = 'Name for new vshale log', description = 'Add a name for the new vshale logs to be created', default_value = 'vshale_pwr')

# End: PWR Description

# Ensure the parameters dictionary is available
if 'parameters' not in locals() and 'parameters' not in globals():
    parameters = pwr_description.get_default_parameters()
    print("Using default parameters for testing")
    print(f"Default parameters: {parameters}")
else:
    print(f"Using provided parameters: {parameters}")

print("Establishing PetrelConnection") 
petrel = PetrelConnection(allow_experimental=True)
print("Connected to {}".format(petrel.get_current_project_name()))



#############################
# Checks for input data
#############################

# Retrieve the selected well using its GUID and check if it's a valid Well object
user_well = petrel.get_petrelobjects_by_guids([parameters['well_id']])[0]
if not isinstance(user_well, Well):
    raise AssertionError("No well selected")
print("Retrieved well by guid")

# Retrieve the selected gamma ray log using its GUID and check if it's a valid WellLog object
try:
    # Validate parameters first
    if 'log_id' not in parameters:
        raise AssertionError("'log_id' not found in parameters")

    log_id = parameters['log_id']
    if not log_id or not isinstance(log_id, str):
        raise AssertionError(f"Invalid log_id in parameters: {log_id}")

    print(f"Attempting to retrieve log with GUID: {log_id}")

    # Get the log object(s) by GUID
    gr_log_list = petrel.get_petrelobjects_by_guids([log_id])

    # Validate the returned list
    if not isinstance(gr_log_list, list):
        raise AssertionError(f"get_petrelobjects_by_guids returned {type(gr_log_list)}, expected list")

    if len(gr_log_list) == 0:
        raise AssertionError("No gamma ray log found for the provided GUID")

    # Get the first (and should be only) element
    gr_log = gr_log_list[0]

    # Double-check that gr_log is not a list (this should not happen, but let's be safe)
    if isinstance(gr_log, list):
        raise AssertionError(f"gr_log is unexpectedly a list: {gr_log}")

    # Check if the object is None
    if gr_log is None:
        raise AssertionError("Gamma ray log GUID returned None - log may not exist in project")

    # Validate the object type
    if not isinstance(gr_log, WellLog):
        raise AssertionError(f"Selected object is not a WellLog. Got type: {type(gr_log)}")

    # Additional validation - check if it has the expected attributes
    if not hasattr(gr_log, 'petrel_name'):
        raise AssertionError(f"WellLog object does not have 'petrel_name' attribute")

    print(f"Successfully retrieved gamma ray log: {gr_log.petrel_name}")

except Exception as e:
    print(f"Error retrieving gamma ray log: {str(e)}")
    print(f"Parameters: {parameters}")
    raise

# Retrieve the selected method and input values for the Vsh calculation
method = parameters['methods']
GRS_input = parameters['grs_input']
GRSh_input = parameters['grsh_input']

# Convert the gamma ray log to a Pandas DataFrame and check if it's not empty
df = gr_log.as_dataframe()
if len(df) == 0:
    raise AssertionError("Gamma ray log is empty. Creation of DataFrame failed")
print("Created Pandas DataFrame from gamma ray input log")



######################################
# Functions for identifying sequences
######################################

# Function to calculate shale volume using the selected method
def calculate_vshale(method, GRS_input, GRSh_input):

    # Calculate the Gamma Ray Index
    GR_index = (df['Value'] - GRS_input) / (GRSh_input - GRS_input)
    GR_index = np.where(GR_index < 0, 0, GR_index)
    GR_index = np.where(GR_index > 1, 1, GR_index)

    # Calculate Vshale based on the selected method
    if method == 0:
        vshale = GR_index
    elif method == 1:
        vshale = 0.083 * (2 ** (3.7 * GR_index) - 1.0)
    elif method == 2:
        vshale = 0.33 * (2 ** (2.0 * GR_index) - 1.0)
    elif method == 3:
        vshale = 1.7 - np.sqrt(3.38 - (GR_index + 0.7) ** 2)
    elif method == 4:
        vshale = GR_index / (3 - 2 * GR_index)
    else:
        vshale = pd.Series([])

    # Ensure Vshale values are within the range 0 to 1
    vshale = np.where(vshale < 0, 0, vshale)  # Set lower limit to 0
    vshale = np.where(vshale > 1, 1, vshale)
    return vshale

# Calculate Vshale using the selected method and add it to the DataFrame
vshale = calculate_vshale(method, GRS_input, GRSh_input)
df['VSH'] = vshale
print("Calculated Vshale values")

# Assign the MD values to a variable
md = df['MD'].to_list()
# Assign the Vsh values to a variable
values = df['VSH'].to_list()



#############################
# Write back to Petrel
#############################

def write_back_log(log_name, log_to_clone, well, md, values, template):

    # Returns list of a given petrel domain object in case of duplicate names
    def get_object_list(x):
        object_list = []
        for i in x:
            if not isinstance(i, list):
                object_list.append(i)
            if isinstance(i, list):
                object_list.extend(i)
        return object_list

    if type(log_to_clone) is DiscreteGlobalWellLog:
        gwl = [i for i in get_object_list(petrel.discrete_global_well_logs) if i.petrel_name == log_name]
    if type(log_to_clone) is GlobalWellLog:
        gwl = [i for i in get_object_list(petrel.global_well_logs) if i.petrel_name == log_name]

    # Find if discrete global well log already exists. If not, create a new one.
    if len(gwl) == 0:
        print('Creating new global well log')
        global_log = log_to_clone.clone(name_of_clone = log_name, template = template)
        gwl = [global_log]

    # Check if well has this log
    well_log = [i for i in get_object_list(well.logs) if i.petrel_name == log_name]

    # If well has well log, overwrite values
    if len(well_log) == 1:
        well_log[0].readonly = False
        well_log[0].set_values(md, values)
        print(f"Values for {log_name} overwritten for {well.petrel_name}")

    # If well does not have this log, create new well log and set values to it.    
    if len(well_log) == 0:
        print('Creating new discete well log')
        well_log = gwl[0].create_well_log(well)
        well_log.readonly = False
        well_log.set_values(md, values)
        print(f"New well log {log_name} created for well {well.petrel_name}")

# Get inputs to write back function, including VShale template
try:
    # Final validation before using gr_log
    print(f"Final check - gr_log type: {type(gr_log)}")
    if isinstance(gr_log, list):
        print(f"ERROR: gr_log is a list with {len(gr_log)} elements: {gr_log}")
        if len(gr_log) > 0:
            print(f"Taking first element of type: {type(gr_log[0])}")
            gr_log = gr_log[0]  # Fix by taking the first element
        else:
            raise AssertionError("gr_log is an empty list")

    if not hasattr(gr_log, 'petrel_name'):
        raise AttributeError(f"gr_log of type {type(gr_log)} does not have petrel_name attribute")

    # Store the petrel_name safely
    stored_petrel_name = str(gr_log.petrel_name)
    print(f"Stored petrel_name: {stored_petrel_name}")

    # Get global well logs and handle potential lists in the collection
    global_well_logs = petrel.global_well_logs
    print(f"Got global_well_logs collection")

    # Helper function to flatten any lists in the collection and find matching log
    def find_global_well_log_by_name(name):
        for item in global_well_logs:
            # Handle both individual objects and lists of objects
            if isinstance(item, list):
                for obj in item:
                    if hasattr(obj, 'petrel_name') and obj.petrel_name == name:
                        return obj
            else:
                if hasattr(item, 'petrel_name') and item.petrel_name == name:
                    return item
        return None

    log_to_clone = find_global_well_log_by_name(stored_petrel_name)
    if log_to_clone is None:
        raise AssertionError(f"Could not find global well log with name: {stored_petrel_name}")

    print(f"Successfully found log_to_clone: {log_to_clone.petrel_name}")

    # Helper function to find template, handling potential lists
    def find_template_by_name(name):
        for item in petrel.templates:
            # Handle both individual objects and lists of objects
            if isinstance(item, list):
                for obj in item:
                    if hasattr(obj, 'petrel_name') and obj.petrel_name == name:
                        return obj
            else:
                if hasattr(item, 'petrel_name') and item.petrel_name == name:
                    return item
        return None

    template = find_template_by_name('VShale')
    if template is None:
        raise AssertionError("Could not find VShale template")

    print(f"Successfully found template: {template.petrel_name}")

except AttributeError as e:
    print(f"Error accessing gr_log.petrel_name: {str(e)}")
    print(f"gr_log type: {type(gr_log)}")
    print(f"gr_log value: {gr_log}")
    raise
except Exception as e:
    print(f"Unexpected error in write back preparation: {str(e)}")
    print(f"gr_log type: {type(gr_log)}")
    print(f"gr_log value: {gr_log}")
    raise

write_back_log(log_name = parameters['suffix'],
               log_to_clone = log_to_clone,
               well = user_well,
               md = md,
               values = values,
               template = template)