# Simple test to check parameter handling logic

# Mock PWR description for testing
class MockWorkflowDescription:
    def get_default_parameters(self):
        return {
            'index_well_id': None,
            'input_wells_ids': None,
            'input_log_ids': None,
            'target_log_id': None,
            'imputation_mode': 0,
            'test_percentage': 25.0,
            'use_depth_feature': True,
            'enable_advanced_models': True,
            'enable_tpot': False,
            'tpot_generations': 5,
            'tpot_population_size': 20,
            'cross_validation': True,
            'cv_folds': 5,
            'create_output_logs': True,
            'output_suffix': '_ML_imputed'
        }

pwr_description = MockWorkflowDescription()

def main():
    """Test main function with parameter handling"""
    
    print("="*80)
    print("PARAMETER HANDLING TEST")
    print("="*80)
    
    # Test the parameter handling logic
    try:
        # Try to access parameters from global scope, if not available use defaults
        try:
            # This will work if parameters was defined globally
            test_params = parameters
            print(f"DEBUG: Using provided parameters: {parameters}")
            print(f"DEBUG: Parameters type: {type(parameters)}")
            print(f"DEBUG: Parameters content: {parameters}")
        except NameError:
            print("DEBUG: Parameters not found in scope, using defaults")
            parameters = pwr_description.get_default_parameters()
            print("Using default parameters for testing")
            print(f"Default parameters: {parameters}")
            print(f"DEBUG: Default parameters type: {type(parameters)}")

    except Exception as e:
        print(f"ERROR: Failed to process parameters: {str(e)}")
        print(f"DEBUG: Exception type: {type(e)}")
        raise
    
    print("Parameter handling test completed successfully!")

if __name__ == "__main__":
    main()
