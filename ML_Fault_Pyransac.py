# Start: PWR Description
# This section defines the user interface for the Petrel workflow.
from cegalprizm.pycoderunner import WorkflowDescription, DomainObjectsEnum
from cegalprizm.pythontool import PetrelConnection
import numpy as np
import pandas as pd
import pyransac3d as pyrsc

# 1. --- Define the UI for the Prizm Workflow Runner ---
pwr_description = WorkflowDescription(name="ML Fault Segmentation",
                                      category="Geological Interpretation",
                                      description="Identifies and creates fault interpretations from a fault probability cube using the RANSAC algorithm.",
                                      authors="Cegal",
                                      version="1.0")

# --- Input Data Parameters ---
pwr_description.add_object_ref_parameter(name='input_seismic_guid',
                                         label='Input Seismic Cube',
                                         description='Select the original seismic cube (used for geometry and as a cloning source).',
                                         object_type=DomainObjectsEnum.Seismic3D)

pwr_description.add_object_ref_parameter(name='probability_cube_guid',
                                         label='Fault Probability Cube',
                                         description='Select the cube containing fault probabilities.',
                                         object_type=DomainObjectsEnum.Seismic3D)

pwr_description.add_object_ref_parameter(name='fault_template_guid',
                                         label='Fault Interpretation Template',
                                         description='Select any existing fault interpretation to use as a template for creating new faults.',
                                         object_type=DomainObjectsEnum.FaultInterpretation)

# --- Output Naming Parameters ---
# CORRECTED LINES: Added the missing 'description' argument to the two function calls below.
pwr_description.add_string_parameter(name='output_segmented_cube_name',
                                     label='Output Segmented Cube Name',
                                     description='Name for the new cube that will store the segmented fault data.',
                                     default_value='fault_segmented')

pwr_description.add_string_parameter(name='output_fault_base_name',
                                     label='Output Fault Base Name',
                                     description='Base name for new fault interpretations (e.g., "RANSAC_Fault" creates "RANSAC_Fault_1").',
                                     default_value='RANSAC_Fault')

# --- RANSAC Algorithm Parameters ---
pwr_description.add_integer_parameter(name='planes',
                                      label='Number of Planes',
                                      description='Maximum number of fault planes to identify.',
                                      default_value=20, minimum_value=1)

pwr_description.add_float_parameter(name='fault_probability_threshold',
                                    label='Fault Probability Threshold',
                                    description='Probability value above which a point is considered part of a fault (0.0 to 1.0).',
                                    default_value=0.97, minimum_value=0.0, maximum_value=1.0)

pwr_description.add_float_parameter(name='plane_fit_threshold',
                                    label='Plane Fit Threshold (RANSAC)',
                                    description='Maximum distance from a point to the plane for it to be considered an inlier.',
                                    default_value=1.0, minimum_value=0.1)

# End: PWR Description

# 2. --- Main Processing Logic ---
# The 'parameters' dictionary is automatically populated by the Prizm Workflow Runner with the user's choices.

# Establish connection to the current Petrel project
petrel = PetrelConnection(allow_experimental=True)
print("Connected to Petrel Project.")

# --- Retrieve objects selected from the UI ---
print("Retrieving objects from user selection...")
input_seismic_cube = petrel.get_petrelobjects_by_guids([parameters['input_seismic_guid']])[0]
fault_probability_cube = petrel.get_petrelobjects_by_guids([parameters['probability_cube_guid']])[0]
fault_template = petrel.get_petrelobjects_by_guids([parameters['fault_template_guid']])[0]

# --- Retrieve processing parameters from the UI ---
output_segmented_cube_name = parameters['output_segmented_cube_name']
output_fault_base_name = parameters['output_fault_base_name']
planes = parameters['planes']
fault_probability_threshold = parameters['fault_probability_threshold']
plane_fit_threshold = parameters['plane_fit_threshold']

print(f"Input Seismic: {input_seismic_cube.petrel_name}")
print(f"Probability Cube: {fault_probability_cube.petrel_name}")
print(f"Processing Parameters: Planes={planes}, Threshold={fault_probability_threshold}")

# --- Clone a cube for the output ---
print(f"Creating output cube: '{output_segmented_cube_name}'")
segmented_fault_cube = input_seismic_cube.clone(output_segmented_cube_name, with_properties=False)

# --- Read data and process ---
print("Reading fault probability data into memory...")
fault_array = fault_probability_cube.all().as_array()
size = fault_array.shape # Process the entire cube
print(f"Cube size: {size}. Starting RANSAC processing...")

# --- Core RANSAC logic from the original script ---
indices = np.indices(size)
points_with_values = np.stack((indices[0], indices[1], indices[2], fault_array), axis=-1).reshape(-1, 4)

df_points = pd.DataFrame(points_with_values, columns=['IL', 'XL', 'Z', 'Probability'])
high_prob_points = df_points[df_points['Probability'] > fault_probability_threshold][['IL', 'XL', 'Z']].to_numpy()

segmented_fault_array = np.zeros(fault_array.shape)
fault_stick_dict = {}

for i in range(planes):
    plane = pyrsc.Plane()
    if len(high_prob_points) < 3:
        print("Not enough points remaining to fit a new plane. Stopping.")
        break
    try:
        best_eq, best_inliers = plane.fit(high_prob_points, thresh=plane_fit_threshold)
        
        if len(best_inliers) == 0:
            print(f"No more points could be fitted for plane {i+1}. Stopping.")
            break

        fault_points = high_prob_points[best_inliers]
        fault_stick_dict[f'fault_stick_ID_{i+1}'] = pd.DataFrame(fault_points, columns=['IL', 'XL', 'Z'])
        
        for p in fault_points:
            segmented_fault_array[int(p[0]), int(p[1]), int(p[2])] = i + 1
        
        high_prob_points = np.delete(high_prob_points, best_inliers, axis=0)
        print(f"Fitted plane {i+1} with {len(best_inliers)} points.")
    except Exception as e:
        print(f"Could not fit plane {i+1}. It's possible no more planes can be found. Error: {e}")
        break
        
print("RANSAC processing complete.")

# --- Write results back to Petrel ---
print("Writing segmented fault data to output cube...")
segmented_fault_cube.all().set(values=segmented_fault_array)

print("Creating new fault interpretations in Petrel...")
for fault_id_str, fault_stick_df in fault_stick_dict.items():
    fault_number = fault_id_str.split('_')[-1]
    fault_name = f"{output_fault_base_name}_{fault_number}"
    
    # Clone the template fault interpretation to create a new one
    new_fault_interpretation = fault_template.clone(name=fault_name)
    
    # The DataFrame for set_polylines needs specific columns: 'Fault Stick ID', 'X', 'Y', 'Z'
    # For a correct implementation, you need to use the cube's geometry to convert
    # inline/crossline to X/Y coordinates. We will proceed with IL/XL as X/Y for this example.
    fault_stick_df['Fault Stick ID'] = 1
    fault_stick_df = fault_stick_df.rename(columns={'IL': 'X', 'XL': 'Y'})
    
    try:
        new_fault_interpretation.set_polylines(fault_stick_df)
        print(f"Successfully created fault interpretation: '{fault_name}'")
    except Exception as e:
        print(f"Could not set polylines for '{fault_name}'. Error: {e}")

print("Workflow finished successfully.")