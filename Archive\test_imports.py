#!/usr/bin/env python3
"""Test imports to identify which one is causing the hang"""

print("Starting import test...")

try:
    print("Testing cegalprizm.pycoderunner...")
    from cegalprizm.pycoderunner import WorkflowDescription, DomainObjectsEnum, TemplateNamesEnum
    print("✓ cegalprizm.pycoderunner imported successfully")
except ImportError as e:
    print(f"✗ cegalprizm.pycoderunner import failed: {e}")

try:
    print("Testing numpy...")
    import numpy as np
    print("✓ numpy imported successfully")
except ImportError as e:
    print(f"✗ numpy import failed: {e}")

try:
    print("Testing pandas...")
    import pandas as pd
    print("✓ pandas imported successfully")
except ImportError as e:
    print(f"✗ pandas import failed: {e}")

try:
    print("Testing sklearn...")
    from sklearn.linear_model import LinearRegression
    print("✓ sklearn imported successfully")
except ImportError as e:
    print(f"✗ sklearn import failed: {e}")

try:
    print("Testing cegalprizm.pythontool...")
    from cegalprizm.pythontool import PetrelConnection, Well, GlobalWellLog, WellLog, DiscreteGlobalWellLog
    print("✓ cegalprizm.pythontool imported successfully")
except ImportError as e:
    print(f"✗ cegalprizm.pythontool import failed: {e}")

print("Import test completed!")
