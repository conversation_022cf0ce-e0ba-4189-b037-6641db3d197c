from cegalprizm.pythontool import PetrelConnection

# Start: PWR Description

from cegalprizm.pycoderunner import WorkflowDescription, DomainObjectsEnum

pwr_description = WorkflowDescription(name="pwr installation test",
                                      category="Bundled workflows",
                                      description="Workflow to test installation of Prizm. Running the workflow will print out the project name and the selected parameters to the output window.",
                                      authors="<EMAIL>",
                                      version="1.0")

# Use the variable pwr_description to define the UI in the Prizm Workflow Runner and let the Petrel user select the input data.
# This creates a Python dictionary 'parameters' with the GUID and/or values of the user's input data.
w = pwr_description.add_object_ref_parameter(name = 'well', label = 'Select well', description = 'Select a well', object_type = DomainObjectsEnum.Well, select_multiple = True)
pwr_description.add_string_parameter(name = 'a_string', label = 'text', description = 'example text input', default_value = 'test' )
pwr_description.add_boolean_parameter(name = "bool", label = 'boolean', description = 'example boolean', default_value = True )

# End: PWR Description


# Ensure the parameters dictionary is available
if 'parameters' not in locals() and 'parameters' not in globals():
    parameters = pwr_description.get_default_parameters()
    
print("Establishing PetrelConnection") 
petrel = PetrelConnection(allow_experimental=True)
print("Connected to {}".format(petrel.get_current_project_name()))

print(parameters)

print('Test successful!')
