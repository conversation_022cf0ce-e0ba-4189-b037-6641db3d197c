# Start: PWR Description

from cegalprizm.pycoderunner import WorkflowDescription, DomainObjectsEnum

# The class WorkflowDescription is used to define the Cegal Prizm workflow
pwr_description = WorkflowDescription(name="Log Selection Demo",
                                      category="Well log analysis",
                                      description="Demonstration of dropdown menus for selecting input and target logs from Petrel project",
                                      authors="BKP_Team@PTM",
                                      version="1.0")

# --- Define the UI for the Prizm Workflow Runner ---

# Well selection
pwr_description.add_object_ref_parameter(name='well_id', 
                                        label='Select Well', 
                                        description='Select a well for log analysis', 
                                        object_type=DomainObjectsEnum.Well)

# Input logs selection - multiple selection dropdown
pwr_description.add_object_ref_parameter(name='input_log_ids', 
                                        label='Input Logs', 
                                        description='Select multiple input logs for analysis (e.g., GR, RHOB, NPHI)', 
                                        object_type=DomainObjectsEnum.WellContinuousLog, 
                                        select_multiple=True, 
                                        linked_input_name='well_id')

# Target log selection - single selection dropdown  
pwr_description.add_object_ref_parameter(name='target_log_id', 
                                        label='Target Log', 
                                        description='Select the target log for analysis (e.g., Vs)', 
                                        object_type=DomainObjectsEnum.WellContinuousLog, 
                                        linked_input_name='well_id')

# Optional: Template-based log selection (for specific log types)
pwr_description.add_object_ref_parameter(name='gamma_ray_log_id', 
                                        label='Gamma Ray Log', 
                                        description='Select a gamma ray log specifically', 
                                        object_type=DomainObjectsEnum.WellContinuousLog, 
                                        template_type='GammaRay', 
                                        linked_input_name='well_id')

# End: PWR Description

# Core libraries
import numpy as np
import pandas as pd
from cegalprizm.pythontool import PetrelConnection, Well, WellLog, GlobalWellLog

def main():
    """Main execution function"""
    
    print("="*60)
    print("LOG SELECTION DEMO")
    print("="*60)
    print()
    
    # Connect to Petrel
    try:
        petrel = PetrelConnection(allow_experimental=True)
        print(f'Connected to Petrel project: {petrel.get_current_project_name()}')
        print()
    except Exception as e:
        print(f"Error connecting to Petrel: {str(e)}")
        return
    
    # Get parameters from UI
    well_id = parameters['well_id']
    input_log_ids = parameters['input_log_ids']
    target_log_id = parameters['target_log_id']
    gamma_ray_log_id = parameters.get('gamma_ray_log_id')  # Optional parameter
    
    print("Selected Parameters:")
    print(f"  - Well ID: {well_id}")
    print(f"  - Input log IDs: {input_log_ids}")
    print(f"  - Target log ID: {target_log_id}")
    if gamma_ray_log_id:
        print(f"  - Gamma ray log ID: {gamma_ray_log_id}")
    print()
    
    try:
        # Get well object
        well_object = petrel.get_petrelobjects_by_guids([well_id])[0]
        print(f"Selected well: {well_object.petrel_name}")
        
        # Get input log objects and names
        input_log_objects = petrel.get_petrelobjects_by_guids(input_log_ids)
        input_log_names = [log.petrel_name for log in input_log_objects if hasattr(log, 'petrel_name')]
        
        print(f"Selected input logs ({len(input_log_names)}):")
        for i, log_name in enumerate(input_log_names, 1):
            print(f"  {i}. {log_name}")
        
        # Get target log object and name
        target_log_object = petrel.get_petrelobjects_by_guids([target_log_id])[0]
        target_log_name = target_log_object.petrel_name
        print(f"Selected target log: {target_log_name}")
        
        # Get gamma ray log if selected
        if gamma_ray_log_id:
            gamma_ray_log_object = petrel.get_petrelobjects_by_guids([gamma_ray_log_id])[0]
            gamma_ray_log_name = gamma_ray_log_object.petrel_name
            print(f"Selected gamma ray log: {gamma_ray_log_name}")
        
        print()
        
        # Demonstrate data loading
        print("Loading log data...")
        all_log_ids = input_log_ids + [target_log_id]
        if gamma_ray_log_id:
            all_log_ids.append(gamma_ray_log_id)
        
        all_log_objects = petrel.get_petrelobjects_by_guids(all_log_ids)
        
        # Get logs dataframe for the selected well
        well_df = well_object.logs_dataframe(all_log_objects)
        
        if not well_df.empty:
            print(f"Successfully loaded data: {len(well_df)} samples")
            print(f"Available columns: {list(well_df.columns)}")
            print()
            
            # Show basic statistics
            print("Data Summary:")
            print(well_df.describe())
            
            # Show data coverage
            print("\nData Coverage:")
            for col in well_df.columns:
                if col != 'MD':  # Skip MD column
                    coverage = (1 - well_df[col].isna().mean()) * 100
                    print(f"  {col}: {coverage:.1f}%")
        else:
            print("No data found for the selected well and logs")
        
    except Exception as e:
        print(f"Error processing selected logs: {str(e)}")
        return
    
    print("\n" + "="*60)
    print("LOG SELECTION DEMO COMPLETED")
    print("="*60)

# Ensure the parameters dictionary is available for testing
if 'parameters' not in locals() and 'parameters' not in globals():
    parameters = pwr_description.get_default_parameters()

if __name__ == "__main__":
    main()
