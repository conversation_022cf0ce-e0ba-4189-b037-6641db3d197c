from cegalprizm.pythontool import PetrelConnection

# Start: PWR Description

from cegalprizm.pycoderunner import WorkflowDescription, DomainObjectsEnum

pwr_description = WorkflowDescription(name = "Grid property ranking",
                                      category = "Bundled workflows",
                                      description = "Ranks cells of a grid in order of the property values as a percentage",
                                      authors = "<EMAIL>",
                                      version = "1.0")

# Use the variable pwr_description to define the UI in the Prizm Workflow Runner and let the Petrel user select the input data.
# This creates a Python dictionary 'parameters' with the GUID and/or values of the user's input data.
pwr_description.add_object_ref_parameter(name = 'input_grid', label = 'Select 3D grid', description = 'Select a 3d model grid', object_type = DomainObjectsEnum.Grid)
pwr_description.add_object_ref_parameter(name = 'input_property', label = 'Select property', description = 'Select a property', object_type = DomainObjectsEnum.GridContinuousProperty, linked_input_name = "input_grid")
pwr_description.add_string_parameter(name = "suffix", label = "Suffix", description = "The suffix to be appended to the name of the returned grid property of ranked values", default_value = "_pct_rank")

# End: PWR Description


# Ensure the parameters dictionary is available
if 'parameters' not in locals() and 'parameters' not in globals():
    parameters = pwr_description.get_default_parameters()
    
# Establish Petrel connection
petrel = PetrelConnection()

# get petrel objects
petrel_objects = petrel.get_petrelobjects_by_guids([parameters["input_grid"], parameters['input_property']])
input_grid = petrel_objects[0]
input_property = petrel_objects[1]



#############################
# Checks for input data
#############################

# checks that objects have been selected
if input_grid is None:
    raise ValueError("No input grid has been selected")
print("Retrieved input grid by guid")

if input_property is None:
    raise ValueError("No input property has been selected")
print("Retrieved input property by guid")



#############################
# Get data, calculate ranking
#############################

# Get pandas dataframe of property data
print("Creating PandasDataFrame")
df = input_property.all().as_dataframe()

# Calculate ranking values
df['pct_rank'] = df['Value'].rank(pct=True)



#############################
# Write back to Petrel
#############################

# Grab template and set name of clone property
template = [i for i in petrel.templates if i.petrel_name == 'Fraction'][0]
name = input_property.petrel_name + parameters['suffix']

# Check if property already exists. If exists, overwrite its values. If not, clone the input property and then write ranked values to it.
property = [i for i in input_grid.properties if i.petrel_name == name]
if len(property) > 0:
    property[0].readonly = False
    property[0].all().set(df.pct_rank.values)
    print("Overwritten values of existing property with the name of: " + name)
if len(property) == 0:
    property_clone = input_property.clone(name, False, template = template)
    property_clone.all().set(df.pct_rank.values)
    print("Ranked grid property value written back with the name of: " + name)
