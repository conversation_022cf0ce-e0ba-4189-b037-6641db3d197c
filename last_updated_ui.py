from cegalprizm.pythontool import PetrelConnection
import pandas as pd

# Start: PWR Description

from cegalprizm.pycoderunner import WorkflowDescription, DomainObjectsEnum, VisualEnum, ParameterState

# The class WorkflowDescription is used to define the Cegal Prizm workflow. It is assigned to a Python variable called 'pwr_description' 
pwr_description = WorkflowDescription(name="Last updated objects",
                                      category="Bundled workflows",
                                      description="Choose between object types and find out the last updated item or items (using top N).",
                                      authors="author@company",
                                      version="1.0")

object_list = {
                1: 'checkshots',
                2: 'faultinterpretations',
                3: 'global_observed_data_sets',
                4: 'grids',
                5: 'grid_properties',
                6: 'horizon_interpretations',
                7: 'horizon_interpretation_3ds',
                8: 'horizon_properties',
                10: 'markercollections',
                11: 'observed_data_sets',
                12: 'pointsets',
                13: 'polylinesets',
                14: 'seismic_2ds',
                15: 'seismic_cubes',
                16: 'seismic_lines',
                17: 'surfaces',
                18: 'surface_attributes',
                19: 'wavelets',
                20: 'wells',
                21: 'well_folders',
                22: 'well_surveys',
                23: 'well_logs'
            }

# Use the variable pwr_description to define the UI in the Prizm Workflow Runner and let the Petrel user select the input data.
# This creates a Python dictionary 'parameters' with the GUID and/or values of the user's input data.
object_type = pwr_description.add_enum_parameter(name = "object_type", label = "Select object type", description = "List of Petrel objects with retrieve history", options = object_list, default_value = 1)
pwr_description.add_integer_parameter(name = "top_n", label = "Top N", description = "The number of items to return, sorted from most recently updated", default_value = 3, minimum_value = 1, maximum_value = 50)

# Checkshots
pwr_description.add_object_ref_parameter(name='checkshots', label='Checkshots', description= 'Choose checkshots', object_type=DomainObjectsEnum.Checkshots, select_multiple=True, linked_visual_parameters={ParameterState(object_type, 1): VisualEnum.Visible})

# Fault Interpretations
pwr_description.add_object_ref_parameter(name='faultinterpretations', label='Fault interpretations', description= 'Choose fault interpretations', object_type=DomainObjectsEnum.FaultInterpretation, select_multiple=True, linked_visual_parameters={ParameterState(object_type, 2): VisualEnum.Visible})

# Global Observed Data Sets
pwr_description.add_object_ref_parameter(name='global_observed_data_sets', label='Global observed data sets', description= 'Choose global observed data sets', object_type=DomainObjectsEnum.ObservedDataset, select_multiple=True, linked_visual_parameters={ParameterState(object_type, 3): VisualEnum.Visible})

# Grids
pwr_description.add_object_ref_parameter(name='grids', label='Grids', description= 'Choose grids', object_type=DomainObjectsEnum.Grid, select_multiple=True, linked_visual_parameters={ParameterState(object_type, 4): VisualEnum.Visible})

# Grid Properties
pwr_description.add_object_ref_parameter(name='grid_properties', label='Grid properties', description= 'Choose grid properties', object_type=DomainObjectsEnum.GridContinuousProperty, select_multiple=True, linked_visual_parameters={ParameterState(object_type, 5): VisualEnum.Visible})

# Horizon Interpretations
pwr_description.add_object_ref_parameter(name='horizon_interpretations', label='Horizon interpretations', description='Choose horizon interpretations', object_type=DomainObjectsEnum.HorizonInterpretation, select_multiple=True, linked_visual_parameters={ParameterState(object_type, 6): VisualEnum.Visible})

# Horizon Interpretation 3Ds
pwr_description.add_object_ref_parameter(name='horizon_interpretation_3ds', label='Horizon interpretation 3Ds', description='Choose horizon interpretation 3Ds', object_type=DomainObjectsEnum.HorizonInterpretation3D, select_multiple=True, linked_visual_parameters={ParameterState(object_type, 7): VisualEnum.Visible})

# Horizon Properties
pwr_description.add_object_ref_parameter(name='horizon_properties', label='Horizon properties', description='Choose horizon properties', object_type=DomainObjectsEnum.HorizonInterpretation3DProperty, select_multiple=True, linked_visual_parameters={ParameterState(object_type, 8): VisualEnum.Visible})

# Marker Collections
pwr_description.add_object_ref_parameter(name='markercollections', label='Marker collections', description='Choose marker collections', object_type=DomainObjectsEnum.WellMarkerCollection, select_multiple=True, linked_visual_parameters={ParameterState(object_type, 10): VisualEnum.Visible})

# Observed Data Sets
pwr_description.add_object_ref_parameter(name='observed_data_sets', label='Observed data sets', description='Choose observed data sets', object_type=DomainObjectsEnum.ObservedDataset, select_multiple=True, linked_visual_parameters={ParameterState(object_type, 11): VisualEnum.Visible})

# Point Sets
pwr_description.add_object_ref_parameter(name='pointsets', label='Point sets', description='Choose point sets', object_type=DomainObjectsEnum.PointSet, select_multiple=True, linked_visual_parameters={ParameterState(object_type, 12): VisualEnum.Visible})

# Polyline Sets
pwr_description.add_object_ref_parameter(name='polylinesets', label='Polyline sets', description='Choose polyline sets', object_type=DomainObjectsEnum.PolylineSet, select_multiple=True, linked_visual_parameters={ParameterState(object_type, 13): VisualEnum.Visible})

# Seismic 2Ds
pwr_description.add_object_ref_parameter(name='sesimic_2ds', label='Seismic 2Ds', description='Choose seismic 2Ds', object_type=DomainObjectsEnum.Seismic2D, select_multiple=True, linked_visual_parameters={ParameterState(object_type, 14): VisualEnum.Visible})

# Seismic Cubes
pwr_description.add_object_ref_parameter(name='seismic_cubes', label='Seismic cubes', description='Choose seismic cubes', object_type=DomainObjectsEnum.Seismic3D, select_multiple=True, linked_visual_parameters={ParameterState(object_type, 15): VisualEnum.Visible})

# Seismic Lines
pwr_description.add_object_ref_parameter(name='seismic_lines', label='Seismic lines', description='Choose seismic lines', object_type=DomainObjectsEnum.SeismicLine, select_multiple=True, linked_visual_parameters={ParameterState(object_type, 16): VisualEnum.Visible})

# Surfaces
pwr_description.add_object_ref_parameter(name='surfaces', label='Surfaces', description='Choose surfaces', object_type=DomainObjectsEnum.Surface, select_multiple=True, linked_visual_parameters={ParameterState(object_type, 17): VisualEnum.Visible})

# Surface Attributes
pwr_description.add_object_ref_parameter(name='surface_attributes', label='Surface attributes', description='Choose surface attributes', object_type=DomainObjectsEnum.SurfaceContinuousAttribute, select_multiple=True, linked_visual_parameters={ParameterState(object_type, 18): VisualEnum.Visible})

# Wavelets
pwr_description.add_object_ref_parameter(name='wavelets', label='Wavelets', description='Choose wavelets', object_type=DomainObjectsEnum.Wavelet, select_multiple=True, linked_visual_parameters={ParameterState(object_type, 19): VisualEnum.Visible})

# Wells
pwr_description.add_object_ref_parameter(name='wells', label='Wells', description='Choose wells', object_type=DomainObjectsEnum.Well, select_multiple=True, linked_visual_parameters={ParameterState(object_type, 20): VisualEnum.Visible})

# Well Folders
pwr_description.add_object_ref_parameter(name='well_folders', label='Well folders', description='Choose well folders', object_type=DomainObjectsEnum.WellsFolder, select_multiple=True, linked_visual_parameters={ParameterState(object_type, 21): VisualEnum.Visible})

# Well Surveys
pwr_description.add_object_ref_parameter(name='well_surveys', label='Well surveys', description='Choose well surveys', object_type=DomainObjectsEnum.WellSurvey, select_multiple=True, linked_visual_parameters={ParameterState(object_type, 22): VisualEnum.Visible})

# Well Logs
pwr_description.add_object_ref_parameter(name='well_logs', label='Well logs', description='Choose well logs', object_type=DomainObjectsEnum.WellContinuousLog, select_multiple=True, linked_visual_parameters={ParameterState(object_type, 23): VisualEnum.Visible})

# End: PWR Description


# Ensure the parameters dictionary is available
if 'parameters' not in locals() and 'parameters' not in globals():
    parameters = pwr_description.get_default_parameters()

print("Establishing PetrelConnection") 
petrel = PetrelConnection(allow_experimental=True)
print("Connected to {}".format(petrel.get_current_project_name()))

# Get all selected objects to a list
selected_obj = parameters['object_type']
obj = object_list[selected_obj]
object_guis = parameters[obj]
petrel_objects = petrel.get_petrelobjects_by_guids(object_guis)


##############################
# Check object for last update
##############################

petrel_objects_list = []
# Check if there are any of the selected object type in the project
if len(petrel_objects) > 0:
    print(f"There are {len(petrel_objects)} {obj} in the project")
    # For each object gets its history
    for petrel_obj in petrel_objects:
        try:
            hist = petrel_obj.retrieve_history()
        except Exception as e:
            print(f'Unable to get history for {petrel_obj}: {e}')

        # Check if the history has any values, if so, grab the last date
        if len(hist) > 0:
                object_info = [petrel_obj.petrel_name, petrel_obj.retrieve_history().Date.values[-1]]
                petrel_objects_list.append(object_info.copy())
        else:
            print(f'No history records for {petrel_obj}')

    # Compile into a dataframe and order by last update
    data = pd.DataFrame(petrel_objects_list, columns=['Petrel Name', 'Last Update'])
    display_data = data.sort_values('Last Update', ascending=False).head(parameters['top_n'])
    
    # Display object name and last update date to the output window in PWR
    for index, row in display_data.iterrows():
        petrel_name = row['Petrel Name']
        last_updated = row['Last Update']
        print(f"{petrel_name} was last updated on {last_updated}")
        print('---------------------------------------------------------------------------------------------------------------------------------------------------------')
else:
    print("There are no " + obj + " in this project, or no objects have been selected")


