# Start: PWR Description

from cegalprizm.pycoderunner import WorkflowDescription, DomainObjectsEnum, TemplateNamesEnum

pwr_description = WorkflowDescription(name="Bruges Discontinuity Attribute",
                                      category="Seismic interpretation",
                                      description="Use this workflow to calculate a seismic discontinuity attribute using the Bruges library.",
                                      authors="author@company",
                                      version="1.0")

# Use the variable pwr_description to define the UI in the Prizm Workflow Runner.
# This creates a Python dictionary 'parameters' with the GUID and/or values of the user's input data.

pwr_description.add_string_parameter(name='new_name_seismic',label='Name for discontinuity cube',description='Give the name of the new processed cube',default_value='Discontinuity')
pwr_description.add_integer_parameter(name='chunk_size',label='Chunk size',description='Define the chunk size for writing result back to Petrel',default_value=150, minimum_value=10, maximum_value=200)
pwr_description.add_object_ref_parameter(name='seismic_id',label='Seismic cube',description='Select a seismic cube',object_type=DomainObjectsEnum.Seismic3D)
pwr_description.add_object_ref_parameter(name='template',label='Seismic template',description='Select a template for the seismic attribute',object_type=DomainObjectsEnum.TemplateContinuous)

# --- UI parameters exclusively for the Discontinuity attribute ---
pwr_description.add_string_parameter(
    name='duration',
    label='Duration (seconds)',
    description='Duration of the analysis window (e.g., 0.016)',
    default_value='0.016'
)

pwr_description.add_string_parameter(
    name='dt',
    label='Sampling Interval (seconds)',
    description='Sampling interval of the seismic data (e.g., 0.004)',
    default_value='0.004'
)

pwr_description.add_integer_parameter(
    name='step_out',
    label='Step Out (traces)',
    description='Number of adjacent traces to include in the calculation',
    default_value=1,
    minimum_value=1,
    maximum_value=5
)

pwr_description.add_enum_parameter(
    name='kind',
    label='Calculation Method',
    description='Method for discontinuity calculation',
    options={0: 'Marfurt', 1: 'Gersztenkorn', 2: 'GST'},
    default_value=2
)

pwr_description.add_string_parameter(
    name='sigma',
    label='Sigma (for GST)',
    description='Sigma value for the Gradient Structure Tensor (GST) method',
    default_value='1'
)

# End: PWR Description


from cegalprizm.pythontool import *
import bruges as bg
import numpy as np

# Connect to Petrel
petrel=PetrelConnection(allow_experimental=True)
print('PetrelConnection established')

# Retrieve the seismic cube and template selected by the user
petrel_objects = petrel.get_petrelobjects_by_guids([parameters['seismic_id'],parameters['template']])

# Assign the user defined name (from the string input parameter) to a variable
newname=parameters['new_name_seismic']
#Verify that a name has been defined
if len(newname) == 0:
    raise ValueError(f"{pwr_description.get_label('new_name_seismic')} : No name defined")
print(f"{pwr_description.get_label('new_name_seismic')} retrieved")

# Assign the user chunk size (from the integer input parameter) to a variable
chunk_size=parameters['chunk_size']
print('Chunk size retrieved')

# Assign the user selected seismic cubes 
selected_seismic_cube=petrel_objects[0]
#Verify that the seismic cube has been selected 
if selected_seismic_cube is None:
    raise ValueError("No seismic cube has been selected")
print('Selected seismic retrieved')

# Assign the user selected template to variables
selected_seismic_template=petrel_objects[1]
#Verify that a template has been selected
if selected_seismic_template is None:
    raise ValueError("No template has been selected")
print('Selected template retrieved')

# Retrieve the seismic extent
cube_dimensions=selected_seismic_cube.extent


def get_chunks(extent, chunk_size):
    
    # Extract the dimensions of the seismic cube.
    i_range, j_range, k_range = extent
    
    # Divide the i dimension into chunks. Each chunk is represented as a tuple (start, end).
    # If the chunk goes beyond the cube dimension, the end is set to i_range-1.

    i_chunks = [(start, min(start + chunk_size, i_range-1)) for start in range(0, i_range, chunk_size)]
    
    # Currently, we are not splitting the j and k dimensions, so we keep them as a single chunk.
    j_chunks = [(0, j_range-1)]
    k_chunks = [(0, k_range-1)]
    
    # Return the combined chunks for all dimensions.
    return [(i, j, k) for i in i_chunks for j in j_chunks for k in k_chunks]

# Retrieve chunks based on cube dimensions and chunk size.
chunks = get_chunks(cube_dimensions, chunk_size)
# Clone the original seismic cube to create a new cube for the attribute
new_cube=selected_seismic_cube.clone(newname,copy_values=False,template=selected_seismic_template)
print('Creating discontinuity cube')

for chunk in chunks:
    # Extract the chunk from the seismic and convert it to an array
    i_range, j_range, k_range = chunk
    data_chunk = selected_seismic_cube.chunk(i_range, j_range, k_range)
    new_cube_array=data_chunk.as_array()
    
    #Extract the chunk from the new cube
    new_chunk=new_cube.chunk(i_range, j_range, k_range)
    print('Processing seismic chunk')
    
    # --- Calculate the discontinuity attribute and write it back to Petrel ---
    
    # Get discontinuity parameters from the UI and convert them to the correct type
    duration_param = float(parameters['duration'])
    dt_param = float(parameters['dt'])
    step_out_param = parameters['step_out']
    kind_param = ['marfurt', 'gersztenkorn', 'gst'][parameters['kind']]
    sigma_param = float(parameters['sigma'])
    
    # Calculate the attribute
    # CORRECTED LINE: Removed the extra .discontinuity
    disc = bg.attribute.discontinuity(new_cube_array,
                                      duration=duration_param,
                                      dt=dt_param,
                                      step_out=step_out_param,
                                      kind=kind_param,
                                      sigma=sigma_param)
    
    # Write the result to the new chunk in Petrel
    new_chunk.set(disc)

print('Discontinuity attribute successfully created')