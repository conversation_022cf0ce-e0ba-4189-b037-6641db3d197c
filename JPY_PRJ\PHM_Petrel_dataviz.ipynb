from cegalprizm.pythontool import PetrelConnection
import matplotlib.pyplot as plt
import numpy as np
import matplotlib
import numpy as np
from IPython.display import Markdown, Image
# import itkwidgets
import ipywidgets as widgets
# from itkwidgets.widget_viewer import Viewer
import math

from platform import python_version

print(python_version())

ptp = PetrelConnection()

print(f'Currently open Petrel project is {ptp.get_current_project_name()}')

w_input = widgets.Dropdown(
    options=ptp.seismic_cubes,
    #value=input_options[0],
    description='Select input:',
    disabled=False,layout={'width':'max-content'},
)
display(w_input)

cube=ptp.seismic_cubes[w_input.label]
cube

cube.retrieve_history()
cube.path

# TWT View
i,j,k = cube.extent # grab the i, j, and k values
array3d = cube.chunk((0,i-1), (0,j-1), (0,k-1)).as_array()

span = 127
x = int((cube.extent.i - 128)/2)
y = int((cube.extent.j - 128)/2)
z = int((cube.extent.k - 128)/2)
arr  = cube.chunk((x,x+span),(y,y+span),(z,z+span)).as_array()



# visualization
array = cube.chunk((0,i-1), (0,j-1), (200,201)).as_array()
plt.imshow(array[:,:,0].T, cmap='seismic')
plt.show()

