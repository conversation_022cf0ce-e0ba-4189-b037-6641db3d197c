# Start: PWR Description

from cegalprizm.pycoderunner import WorkflowDescription, DomainObjectsEnum

pwr_description = WorkflowDescription(name="Petrel Log Creation Diagnostic",
                                      category="Diagnostics",
                                      description="Diagnostic tool to test log creation functionality in Petrel",
                                      authors="BKP_Team@PTM",
                                      version="1.0")

# --- Define the UI for the Prizm Workflow Runner ---
pwr_description.add_object_ref_parameter(name='test_well_id', 
                                        label='Test Well', 
                                        description='Select a well for testing log creation', 
                                        object_type=DomainObjectsEnum.Well)

pwr_description.add_object_ref_parameter(name='source_log_id', 
                                        label='Source Log', 
                                        description='Select a source log to clone', 
                                        object_type=DomainObjectsEnum.WellContinuousLog, 
                                        linked_input_name='test_well_id')

pwr_description.add_string_parameter(name='test_suffix', 
                                   label='Test Suffix', 
                                   description='Suffix for the test log', 
                                   default_value='_test')

# End: PWR Description

import numpy as np
import pandas as pd
from cegalprizm.pythontool import PetrelConnection, Well, WellLog, GlobalWellLog

def main():
    """Main diagnostic function"""
    
    print("="*60)
    print("PETREL LOG CREATION DIAGNOSTIC")
    print("="*60)
    print()
    
    # Connect to Petrel
    try:
        petrel = PetrelConnection(allow_experimental=True)
        print(f'✓ Connected to Petrel project: {petrel.get_current_project_name()}')
        print()
    except Exception as e:
        print(f"✗ Error connecting to Petrel: {str(e)}")
        return
    
    # Get parameters
    test_well_id = parameters['test_well_id']
    source_log_id = parameters['source_log_id']
    test_suffix = parameters['test_suffix']
    
    try:
        # Get well and log objects
        well_object = petrel.get_petrelobjects_by_guids([test_well_id])[0]
        source_log_object = petrel.get_petrelobjects_by_guids([source_log_id])[0]
        
        print(f"Test well: {well_object.petrel_name}")
        print(f"Source log: {source_log_object.petrel_name}")
        print(f"Test suffix: {test_suffix}")
        print()
        
        # Test 1: Check if we can access the source log data
        print("Test 1: Accessing source log data...")
        try:
            source_df = source_log_object.as_dataframe()
            print(f"✓ Source log data loaded: {len(source_df)} samples")
            print(f"  Columns: {list(source_df.columns)}")
            print(f"  Data range: {source_df['Value'].min():.2f} to {source_df['Value'].max():.2f}")
        except Exception as e:
            print(f"✗ Error accessing source log data: {str(e)}")
            return
        print()
        
        # Test 2: Find the global well log
        print("Test 2: Finding global well log...")
        try:
            source_log_name = source_log_object.petrel_name
            global_logs = [log for log in petrel.global_well_logs 
                          if log.petrel_name == source_log_name]
            
            if global_logs:
                global_log = global_logs[0]
                print(f"✓ Found global well log: {global_log.petrel_name}")
            else:
                print(f"✗ No global well log found for: {source_log_name}")
                return
        except Exception as e:
            print(f"✗ Error finding global well log: {str(e)}")
            return
        print()
        
        # Test 3: Create new global well log by cloning
        print("Test 3: Creating new global well log...")
        try:
            new_log_name = source_log_name + test_suffix
            print(f"  New log name: {new_log_name}")
            
            # Check if it already exists
            existing_logs = [log for log in petrel.global_well_logs 
                           if log.petrel_name == new_log_name]
            
            if existing_logs:
                print(f"  Log already exists: {new_log_name}")
                new_global_log = existing_logs[0]
            else:
                print(f"  Creating new global log...")
                new_global_log = global_log.clone(name_of_clone=new_log_name)
                print(f"✓ Successfully created global log: {new_log_name}")
        except Exception as e:
            print(f"✗ Error creating global well log: {str(e)}")
            import traceback
            print(f"  Traceback: {traceback.format_exc()}")
            return
        print()
        
        # Test 4: Create well log for the specific well
        print("Test 4: Creating well log for specific well...")
        try:
            # Helper function
            def get_object_list(x):
                object_list = []
                for i in x:
                    if not isinstance(i, list):
                        object_list.append(i)
                    if isinstance(i, list):
                        object_list.extend(i)
                return object_list
            
            # Check if well already has this log
            existing_well_logs = [i for i in get_object_list(well_object.logs) 
                                 if i.petrel_name == new_log_name]
            
            if existing_well_logs:
                print(f"  Well already has log: {new_log_name}")
                well_log = existing_well_logs[0]
            else:
                print(f"  Creating new well log for: {well_object.petrel_name}")
                well_log = new_global_log.create_well_log(well_object)
                print(f"✓ Successfully created well log")
        except Exception as e:
            print(f"✗ Error creating well log: {str(e)}")
            import traceback
            print(f"  Traceback: {traceback.format_exc()}")
            return
        print()
        
        # Test 5: Set values to the well log
        print("Test 5: Setting values to well log...")
        try:
            # Get some test data (use first 100 points from source)
            test_data = source_df.head(100).copy()
            md_values = test_data.index.tolist()
            log_values = test_data['Value'].tolist()
            
            # Remove any NaN values
            valid_pairs = [(md, val) for md, val in zip(md_values, log_values) 
                          if not (pd.isna(md) or pd.isna(val))]
            
            if valid_pairs:
                md_clean = [pair[0] for pair in valid_pairs]
                val_clean = [pair[1] for pair in valid_pairs]
                
                print(f"  Setting {len(md_clean)} data points...")
                well_log.readonly = False
                well_log.set_values(md_clean, val_clean)
                print(f"✓ Successfully set values to well log")
            else:
                print(f"✗ No valid data points to set")
        except Exception as e:
            print(f"✗ Error setting values: {str(e)}")
            import traceback
            print(f"  Traceback: {traceback.format_exc()}")
            return
        print()
        
        # Test 6: Verify the log was created
        print("Test 6: Verifying log creation...")
        try:
            # Refresh and check if the log appears
            updated_global_logs = [log for log in petrel.global_well_logs 
                                 if log.petrel_name == new_log_name]
            
            updated_well_logs = [i for i in get_object_list(well_object.logs) 
                               if i.petrel_name == new_log_name]
            
            print(f"  Global logs found: {len(updated_global_logs)}")
            print(f"  Well logs found: {len(updated_well_logs)}")
            
            if updated_global_logs and updated_well_logs:
                print(f"✓ Log creation verified successfully!")
                print(f"  New log name: {new_log_name}")
                print(f"  Well: {well_object.petrel_name}")
            else:
                print(f"✗ Log creation verification failed")
        except Exception as e:
            print(f"✗ Error verifying log creation: {str(e)}")
        print()
        
    except Exception as e:
        print(f"✗ Error in diagnostic: {str(e)}")
        import traceback
        print(f"Traceback: {traceback.format_exc()}")
    
    print("="*60)
    print("DIAGNOSTIC COMPLETED")
    print("="*60)

# Ensure the parameters dictionary is available for testing
if 'parameters' not in locals() and 'parameters' not in globals():
    parameters = pwr_description.get_default_parameters()

if __name__ == "__main__":
    main()
