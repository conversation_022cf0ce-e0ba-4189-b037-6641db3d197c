# Core libraries
import numpy as np
import pandas as pd
import warnings
warnings.filterwarnings('ignore')

# ML libraries
from xgboost import XGBRegressor
from lightgbm import LGBMRegressor
from catboost import CatBoostRegressor
from sklearn.model_selection import train_test_split
from sklearn.metrics import mean_absolute_error

# Plotting (optional)
import plotly.express as px
from cegal.welltools.plotting import CegalWellPlotter as cwp

# Petrel connection
from cegalprizm.pythontool import PetrelConnection

petrel = PetrelConnection(allow_experimental=True)
print(f'Connected to Petrel project: {petrel.get_current_project_name()}')

# Pick all wells in project
wells = [w for w in petrel.wells]
print(f'Total wells detected: {len(wells)}')

# Choose global well logs to work with
LOG_NAMES = ['GR', 'Vp', 'RHOB', 'NPHI', 'Vs']   # edit as required
logs = [gwl for gwl in petrel.global_well_logs if gwl.petrel_name in LOG_NAMES]
print(f'Using {len(logs)} global logs: {[g.petrel_name for g in logs]}')

well_data = pd.DataFrame()

for w in wells:
    df = w.logs_dataframe(logs)      # returns MD‑indexed DataFrame
    df['WELL'] = w.petrel_name
    well_data = pd.concat([well_data, df])

well_data.reset_index(drop=False, inplace=True)  # MD becomes column
print(f'Combined DataFrame shape: {well_data.shape}')
display(well_data.head())

GR_MAX = 300
well_data['GR'] = np.where(well_data['GR'] <= GR_MAX, well_data['GR'], np.nan)
if 'NPHI' in well_data.columns:
    well_data['NPHI'] = np.where(
        (well_data['NPHI'] >= 0) & (well_data['NPHI'] <= 1),
        well_data['NPHI'], np.nan
    )
display(well_data.describe().T)

coverage = 1.0 - well_data[LOG_NAMES].isna().mean()
fig = px.bar(coverage, labels={'value':'Coverage'}, title='Relative data coverage')
fig.show()

def impute_logs(df, depth_col, feature_cols, targets):
    """Return DataFrame with *_pred, *_imputed, *_error columns."""
    res = df.copy()
    boosters = [
        ('XGB', XGBRegressor(n_estimators=300, learning_rate=0.05, tree_method='hist')),
        ('LGBM', LGBMRegressor(n_estimators=300)),
        ('Cat', CatBoostRegressor(verbose=0))
    ]
    feature_set = feature_cols + [depth_col]

    for tgt in targets:
        print(f'--- {tgt} ---')
        train = res[res[tgt].notna()][feature_set + [tgt]].copy()
        if train.empty:
            print('No training data, skipping.')
            continue
        X = train.drop(columns=[tgt]).apply(lambda c: c.fillna(c.mean()), axis=0)
        y = train[tgt]

        Xtr, Xval, ytr, yval = train_test_split(X, y, test_size=0.25, random_state=42)
        best_model, best_name, best_mae = None, None, float("inf")
        for name, model in boosters:
            model.fit(Xtr, ytr)
            mae = mean_absolute_error(yval, model.predict(Xval))
            if mae < best_mae:
                best_model, best_name, best_mae = model, name, mae
        print(f'Chosen model: {best_name} (MAE={best_mae:.2f})')

        X_full = res[feature_set].apply(lambda c: c.fillna(c.mean()), axis=0)
        preds = best_model.predict(X_full)
        res[f'{tgt}_pred'] = preds
        res[f'{tgt}_imputed'] = res[tgt].fillna(preds)
        res[f'{tgt}_error'] = np.abs(res[tgt] - preds) / res[tgt] * 100
    return res

DEPTH_COL = 'MD'
FEATURES   = [c for c in LOG_NAMES if c != 'Vs']
results = impute_logs(well_data, DEPTH_COL, FEATURES, targets=['Vs'])
results.head()

def write_back_to_petrel(results_df, log_name_in_results, clone_from='Vs'):
    """Clone an existing log (or first available log) and overwrite with imputed values."""
    for w in wells:
        print(f'Updating {w.petrel_name}')
        well_df = results_df[results_df['WELL'] == w.petrel_name].set_index('MD')
        md = well_df.index.to_numpy()
        values = well_df[log_name_in_results].to_numpy()

        # Find a log to clone (or existing target)
        target_logs = [log for log in w.logs if log.petrel_name == log_name_in_results]
        if target_logs:
            log_obj = target_logs[0]
        else:
            # Clone template log
            template = [log for log in w.logs if log.petrel_name == clone_from][0]
            log_obj = template.clone(w, log_name_in_results)

        petrel_log_ref = petrel.well_logs[log_obj.path]
        petrel_log_ref.readonly = False
        petrel_log_ref.set_values(md, values)

# Uncomment to execute
# write_back_to_petrel(results, 'Vs_imputed')