# Start: PWR Description

from cegalprizm.pycoderunner import WorkflowDescription,DomainObjectsEnum,MeasurementNamesEnum,TemplateNamesEnum

pwr_description = WorkflowDescription(name="Fault prediction CR",
                                      category="ML",
                                      description="Deep learning fault prediction using various confidence rating",
                                      authors="<EMAIL>",
                                      version="0.2")


pwr_description.add_object_ref_parameter(name='seismic_id',label='Seismic input cube',description='Select a seismic cube',object_type=DomainObjectsEnum.Seismic3D)
pwr_description.add_float_parameter(name='cr_input',label='Confidence rating',description='Confidence rating for predicting fault',default_value=0.5, minimum_value=0, maximum_value=0.99)
pwr_description.add_string_parameter(name='new_name_seismic',label='Name for fault prediction cube',description='Give the name for the new fault prediction cube',default_value='fault_prediction')


# End: PWR Description

import math
from tensorflow.keras.models import load_model, model_from_json
import numpy as np
import tensorflow as tf
import os
import numpy as np
import pandas as pd
from cegalprizm.pythontool import PetrelConnection

# Connect to Petrel
ptp = PetrelConnection()
print('Petrel connection established')

# Retrieve the seismic cube and template selected by the user
petrel_objects = ptp.get_petrelobjects_by_guids([parameters['seismic_id']])

# Assign the user defined name (from the string input parameter) to a variable
newname=parameters['new_name_seismic']
#Verify that a name has been defined
if len(newname) == 0:
    raise ValueError(f"{pwr_description.get_label('new_name_seismic')} : No name defined")
print(f"{pwr_description.get_label('new_name_seismic')} retrieved")

# Assign the user selected seismic cube to a variable
cube=petrel_objects[0]
#Verify that the seismic cube has been selected 
if cube is None:
    raise ValueError("No seismic cube has been selected")
print('Selected seismic retrieved')


span = 127
x = int((cube.extent.i - 128) / 2)
y = int((cube.extent.j - 128) / 2)
z = int((cube.extent.k - 128) / 2)
arr = cube.chunk((x, x + span), (y, y + span), (z, z + span)).as_array()

print("Created seismic array")

# Load model without compiling and recompile with new optimizer (adj)
loaded_model = load_model(
    "fseg-70.hdf5",
    custom_objects={"cross_entropy_balanced": tf.keras.losses.BinaryCrossentropy()},
    compile=False,
)
loaded_model.compile(
    optimizer=tf.keras.optimizers.Adam(learning_rate=1e-4),
    loss=tf.keras.losses.BinaryCrossentropy(),
)
print("Loaded pretrained model")

# Threshold for large-displacement faults
# LARGE_DISPLACEMENT_THRESHOLD = 0.98  # Adjust based on your dataset and fault characteristics
LARGE_DISPLACEMENT_THRESHOLD = parameters['cr_input'] # User defined


def fault_attribute_calculator(arr, loaded_model, threshold):
    # Preprocess the seismic data
    arr = np.rot90(arr, 1, (0, 2))
    arr = (arr - arr.mean()) / arr.std()
    n1, n2, n3 = 128, 128, 128
    gx = np.reshape(arr, (1, n1, n2, n3, 1))
    
    # Model prediction
    Y = loaded_model.predict(gx, verbose=1)
    Y = Y.reshape((n1, n2, n3))
    
    # Apply threshold to keep only large-displacement faults
    Y[Y < threshold] = 0  # Suppress low-displacement faults
    return np.rot90(Y, -1, (0, 2))


# Retrieve the Variance template
var_temp = [i for i in ptp.templates if i.petrel_name == "Seismic - Variance"][0]

# Create new seismic cube
cube_fault_prediction = cube.clone(newname, copy_values=False, template=var_temp)

print("Created seismic clone")


def apply_calculator(
    src_cube, dst_cube, calculator, loaded_model, chunk_size=(128, 128, 128), threshold=0.7
):
    import math

    loaded_model = loaded_model

    # Define constants for blending and overlap handling
    a = 0.5
    overlapp = 0.1

    # Retrieve the dimensions of the source cube
    m1 = src_cube.extent.i
    m2 = src_cube.extent.j
    m3 = src_cube.extent.k

    # Retrieve the chunk size
    n1 = chunk_size[0]
    n2 = chunk_size[1]
    n3 = chunk_size[2]

    # Calculate the number of chunks in each dimension
    x_count = math.ceil(2 * m1 / n1)
    y_count = math.ceil(2 * m2 / n2)
    z_count = math.ceil(2 * m3 / n3)

    # Loop over chunks in the x-direction
    for x in range(x_count):
        print(
            f"Applying fault prediction on entire seismic cube and merging overlap. Looping over in x,y and z direction. This is loop {x} out of {x_count-1} in x-direction"
        )
        # Skip chunks that exceed the cube boundary in the x-direction
        if x * (n1 / (1 + overlapp)) > (m1 - n1 / 2 - 1):
            continue
        # Loop over chunks in the y-direction
        for y in range(y_count):
            # Skip chunks that exceed the cube boundary in the y-direction
            if y * (n2 / (1 + overlapp)) > (m2 - n2 / 2 - 1):
                continue
            # Loop over chunks in the z-direction
            for z in range(z_count):
                # Skip chunks that exceed the cube boundary in the z-direction
                if z * (n3 / (1 + overlapp)) > (m3 - n3 / 2 - 1):
                    continue
                # Calculate the offset for each dimension
                x_offset = math.floor(min(x * (n1 / (1 + overlapp)), m1 - n1 - 1))
                y_offset = math.floor(min(y * (n2 / (1 + overlapp)), m2 - n2 - 1))
                z_offset = math.floor(min(z * (n3 / (1 + overlapp)), m3 - n3 - 1))
                # Define the range for each dimension
                x_range = (x_offset + 1, x_offset + n1)
                y_range = (y_offset + 1, y_offset + n2)
                z_range = (z_offset + 1, z_offset + n3)
                # Retrieve the chunk of data from the source cube
                src_data = src_cube.chunk(x_range, y_range, z_range).as_array()
                # Process the chunk with the fault attribute calculator
                with dst_cube.chunk(x_range, y_range, z_range).values() as dst:
                    result = calculator(src_data, loaded_model, threshold)
                    # Apply a mask to blend the results and existing data
                    mask = dst == 0
                    dst[mask] = result[mask]
                    dst[2:-2, 2:-2, 2:-2] = a * result[2:-2, 2:-2, 2:-2] + (1 - a) * dst[
                        2:-2, 2:-2, 2:-2
                    ]


# Apply fault attribute calculator with threshold
apply_calculator(
    cube, cube_fault_prediction, fault_attribute_calculator, loaded_model, threshold=LARGE_DISPLACEMENT_THRESHOLD
)
print("Entire seismic cube successfully processed")

