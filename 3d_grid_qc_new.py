import numpy as np
import pandas as pd
from cegalprizm.pythontool import PetrelConnection

# Start: PWR Description

from cegalprizm.pycoderunner import WorkflowDescription, DomainObjectsEnum

pwr_description = WorkflowDescription(name="3D property p-values",
                                      category="Bundled workflows",
                                      description="Calculates the p10-p50-p90 distribution of multiple realizations of a model property",
                                      authors="<EMAIL>",
                                      version="1.0")

# Use the variable pwr_description to define the UI in the Prizm Workflow Runner and let the Petrel user select the input data.
# This creates a Python dictionary 'parameters' with the GUID and/or values of the user's input data.
pwr_description.add_object_ref_parameter(name = 'input_grid', label = 'Select 3D grid', description = 'Select a 3d model grid', object_type = DomainObjectsEnum.Grid)
pwr_description.add_object_ref_parameter(name = 'input_property', label = 'Select properties', description = 'Select properties', object_type = DomainObjectsEnum.GridContinuousProperty, linked_input_name = "input_grid", select_multiple = True)

# End: PWR Description


# Ensure the parameters dictionary is available
if 'parameters' not in locals() and 'parameters' not in globals():
    parameters = pwr_description.get_default_parameters()
    
print("Establishing PetrelConnection") 
petrel = PetrelConnection(allow_experimental=True)
print("Connected to {}".format(petrel.get_current_project_name()))



#############################
# Checks for input data
#############################

input_grid = petrel.get_petrelobjects_by_guids([parameters["input_grid"]])[0]
if input_grid is None:
    raise ValueError("No input_grid has been selected")
print("Retrieved input grid by guid")

grid_property_list = petrel.get_petrelobjects_by_guids(parameters["input_property"])
if len(grid_property_list) == 0:
    raise ValueError("No grid properties have been selected")
print("Retrieved grid properties by guid")

if any(item.template != grid_property_list[0].template for item in grid_property_list):
    raise ValueError("Not all properties selected have the same template. This workflow only returns valid results for properties with the same template.")



##################################
# Get data & calculate percentiles
##################################

# Create df of all properties selected
print("Creating PandasDataFrame")
df = grid_property_list[0].all().as_dataframe()
df = df.drop(['Value'], axis = 1)
for i in range(0, len(grid_property_list)):
    prop_df = grid_property_list[i].all().as_dataframe()
    prop_df = prop_df.rename(columns={"Value": grid_property_list[i].petrel_name})
    df = pd.concat([df, prop_df[grid_property_list[i].petrel_name]], axis=1)
    print("Added "+str(i+1)+" property realizations to the DataFrame")
print("DataFrame created")
    
percentiles = [0.1, 0.5, 0.9]
results = df.copy()
for i in percentiles:
    colname = 'P'+str(int(i*100))
    results[colname] = np.quantile(df[df.columns[3:]].values, i, axis=1)



#############################
# Write back to Petrel
#############################

# Create 3 new properties in Petrel
print('Creating new grid properties in model input tree')
p10_prop = grid_property_list[0].clone('_'+grid_property_list[0].template+'_p10', copy_values=False)
p50_prop = grid_property_list[0].clone('_'+grid_property_list[0].template+'_p50', copy_values=False)
p90_prop = grid_property_list[0].clone('_'+grid_property_list[0].template+'_p90', copy_values=False)

# Create 3 new chunks based on the I,J,K ranges from the filtered df
P10_chunk = p10_prop.all()
P50_chunk = p50_prop.all()
P90_chunk = p90_prop.all()

#Write the results back to Petrel
print("Writing p10, p50, 90 values to new grid properties")
P10_chunk.set(results['P10'].values)
P50_chunk.set(results['P50'].values)
P90_chunk.set(results['P90'].values)

print('Finished script')