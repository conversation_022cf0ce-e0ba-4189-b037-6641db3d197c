# Copyright 2024 Cegal AS
# All rights reserved.
# Unauthorized copying of this file, via any medium is strictly prohibited.



from .petrelobject_grpc import PetrelObjectGrpc
from .borehole_grpc import BoreholeGrpc
from cegalprizm.pythontool.grpc import petrelinterface_pb2
import typing
if typing.TYPE_CHECKING:
    from cegalprizm.pythontool.petrelconnection import PetrelConnection
    from cegalprizm.pythontool.oophub.borehole_collection_hub import BoreholeCollectionHub

class BoreholeCollectionGrpc(PetrelObjectGrpc):
    def __init__(self, guid: str, petrel_connection: "PetrelConnection"):
        super(BoreholeCollectionGrpc, self).__init__('borehole collection', guid, petrel_connection)
        self._guid = guid
        self._plink = petrel_connection
        self._invariant_content = {}
        self._channel = typing.cast("BoreholeCollectionHub", petrel_connection._service_boreholecollection)

    def GetWells(self, recursive):
        self._plink._opened_test()
        request = petrelinterface_pb2.BoreholeCollection_GetWells_Request(
            Guid = petrelinterface_pb2.PetrelObjectGuid(guid = self._guid, sub_type = self._sub_type),
            Recursive = recursive
        )
        responses = self._channel.BoreholeCollection_GetWells(request)
        return [BoreholeGrpc(response.guid, self._plink) for response in responses]
    
    def GetBoreholeCollections(self):
        self._plink._opened_test()
        request = petrelinterface_pb2.PetrelObjectGuid(guid = self._guid, sub_type = self._sub_type)
        responses = self._channel.BoreholeCollection_GetBoreholeCollections(request)
        return [BoreholeCollectionGrpc(response.guid, self._plink) for response in responses]