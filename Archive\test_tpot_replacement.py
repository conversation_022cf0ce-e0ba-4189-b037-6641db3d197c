#!/usr/bin/env python3
"""
Test script to verify TPOT replacement in Missing_Log_Imputation_ML.py
"""

import sys
import traceback

def test_imports():
    """Test that all imports work correctly"""
    print("Testing imports...")
    
    try:
        import Missing_Log_Imputation_ML as ml_script
        print("✓ Successfully imported Missing_Log_Imputation_ML")
        
        # Check TPOT availability
        if hasattr(ml_script, 'TPOT_AVAILABLE'):
            print(f"✓ TPOT_AVAILABLE = {ml_script.TPOT_AVAILABLE}")
        else:
            print("✗ TPOT_AVAILABLE not found")
            
        # Check that auto-sklearn is not referenced
        if hasattr(ml_script, 'AUTOSKLEARN_AVAILABLE'):
            print("✗ AUTOSKLEARN_AVAILABLE still exists (should be removed)")
        else:
            print("✓ AUTOSKLEARN_AVAILABLE successfully removed")
            
        return True
        
    except Exception as e:
        print(f"✗ Import failed: {e}")
        traceback.print_exc()
        return False

def test_automl_framework():
    """Test the AutoML framework functionality"""
    print("\nTesting AutoML framework...")
    
    try:
        import Missing_Log_Imputation_ML as ml_script
        
        # Create AutoML framework instance
        framework = ml_script.AutoMLFramework(random_seed=42)
        print("✓ Successfully created AutoMLFramework instance")
        
        # Get available models
        models = framework.get_automl_models(time_limit=60)
        print(f"✓ Available AutoML models: {list(models.keys())}")
        
        if 'TPOT' in models:
            print("✓ TPOT model is available")
            tpot_model = models['TPOT']
            print(f"✓ TPOT model type: {type(tpot_model)}")
        else:
            print("✗ TPOT model not found in available models")
            
        if 'AutoSklearn' in models:
            print("✗ AutoSklearn still found in models (should be removed)")
        else:
            print("✓ AutoSklearn successfully removed from models")
            
        return True
        
    except Exception as e:
        print(f"✗ AutoML framework test failed: {e}")
        traceback.print_exc()
        return False

def test_configuration():
    """Test that configuration parameters are updated correctly"""
    print("\nTesting configuration...")
    
    try:
        import Missing_Log_Imputation_ML as ml_script
        
        # Check if the script can be imported and basic functionality works
        generator = ml_script.SyntheticWellLogGenerator(random_seed=42)
        print("✓ Successfully created SyntheticWellLogGenerator")
        
        ml_framework = ml_script.MLImputationFramework(random_seed=42)
        print("✓ Successfully created MLImputationFramework")
        
        automl_framework = ml_script.AutoMLFramework(random_seed=42)
        print("✓ Successfully created AutoMLFramework")
        
        return True
        
    except Exception as e:
        print(f"✗ Configuration test failed: {e}")
        traceback.print_exc()
        return False

def main():
    """Run all tests"""
    print("="*60)
    print("TESTING TPOT REPLACEMENT IN MISSING LOG IMPUTATION SCRIPT")
    print("="*60)
    
    tests = [
        test_imports,
        test_automl_framework,
        test_configuration
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"✗ Test {test.__name__} failed with exception: {e}")
    
    print("\n" + "="*60)
    print(f"TEST RESULTS: {passed}/{total} tests passed")
    
    if passed == total:
        print("✓ All tests passed! TPOT replacement was successful.")
        print("\nKey changes verified:")
        print("  - Auto-sklearn imports removed")
        print("  - TPOT is now the primary AutoML framework")
        print("  - AutoML framework updated to use TPOT only")
        print("  - Configuration descriptions updated")
        print("  - AutoML enabled by default in standalone mode")
    else:
        print("✗ Some tests failed. Please review the issues above.")
    
    print("="*60)
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
